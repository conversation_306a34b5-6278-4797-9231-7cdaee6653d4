import React, { createContext, useContext, ReactNode } from 'react'
import { WorkflowNodeData } from '../types/workflow'

interface WorkflowContextType {
  updateNodeData: (nodeId: string, data: Partial<WorkflowNodeData>) => void
}

const WorkflowContext = createContext<WorkflowContextType | undefined>(undefined)

interface WorkflowProviderProps {
  children: ReactNode
  updateNodeData: (nodeId: string, data: Partial<WorkflowNodeData>) => void
}

export const WorkflowProvider: React.FC<WorkflowProviderProps> = ({
  children,
  updateNodeData,
}) => {
  return (
    <WorkflowContext.Provider value={{ updateNodeData }}>
      {children}
    </WorkflowContext.Provider>
  )
}

export const useWorkflowContext = () => {
  const context = useContext(WorkflowContext)
  if (context === undefined) {
    throw new Error('useWorkflowContext must be used within a WorkflowProvider')
  }
  return context
}
