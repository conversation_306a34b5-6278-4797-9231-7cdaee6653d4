import { v4 as uuidv4 } from 'uuid'
import { WorkflowNode, NodeType, WorkflowNodeData } from '../types/workflow'

export const createNode = (
  type: NodeType,
  position: { x: number; y: number },
  data: Partial<WorkflowNodeData> = {}
): WorkflowNode => {
  const defaultData: Record<NodeType, WorkflowNodeData> = {
    start: { label: 'Start' },
    process: { label: 'Process', description: 'Enter process description' },
    decision: { label: 'Decision', description: 'Enter condition' },
    end: { label: 'End' }
  }

  return {
    id: uuidv4(),
    type,
    position,
    data: { ...defaultData[type], ...data },
    draggable: true,
    selectable: true,
  }
}

export const validateWorkflow = (nodes: WorkflowNode[]): string[] => {
  const errors: string[] = []

  const startNodes = nodes.filter(node => node.type === 'start')
  const endNodes = nodes.filter(node => node.type === 'end')

  if (startNodes.length === 0) {
    errors.push('Workflow must have at least one start node')
  }

  if (startNodes.length > 1) {
    errors.push('Workflow can only have one start node')
  }

  if (endNodes.length === 0) {
    errors.push('Workflow must have at least one end node')
  }

  return errors
}

export const getNodeColor = (type: NodeType): string => {
  const colors = {
    start: 'bg-green-500',
    process: 'bg-blue-500',
    decision: 'bg-yellow-500',
    end: 'bg-red-500'
  }
  return colors[type]
}

export const getNodeIcon = (type: NodeType): string => {
  const icons = {
    start: '▶️',
    process: '⚙️',
    decision: '🔀', // Thay đổi icon cho decision node
    end: '🏁'
  }
  return icons[type]
}
