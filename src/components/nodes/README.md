# BaseNode - Node Component Architecture

## Tổng quan

BaseNode là một component cơ sở được thiết kế để dùng chung cho tất cả các loại node trong workflow builder. Nó cung cấp một cách tiếp cận nhất quán và có thể tái sử dụng để tạo ra các node với các tính năng chung.

## Cấu trúc

### BaseNode Component
- **File**: `BaseNode.tsx`
- **M<PERSON><PERSON> đích**: Component cơ sở chứa logic chung cho tất cả các node
- **Tính năng**:
  - Hiển thị label và description
  - Chế độ edit inline (nếu được bật)
  - Quản lý handles (input/output connections)
  - Styling nhất quán

### Node Configurations
- **File**: `nodeConfigs.ts`
- **<PERSON><PERSON><PERSON> đích**: <PERSON><PERSON><PERSON> nghĩa cấu hình cho từng loại node
- **Bao gồm**:
  - <PERSON><PERSON><PERSON> sắc và styling
  - <PERSON><PERSON>nh dạng (rectangle hoặc diamond)
  - <PERSON><PERSON> trí và style của handles
  - T<PERSON>h năng edit có được bật hay không
  - Icon cho từng loại node

### Workflow Context
- **File**: `../contexts/WorkflowContext.tsx`
- **Mục đích**: Cung cấp context để các node có thể cập nhật dữ liệu
- **Chức năng**: Truyền `updateNodeData` function xuống các node

## Cách sử dụng

### 1. Tạo một node mới

```tsx
import React from 'react'
import { NodeProps } from '@xyflow/react'
import { WorkflowNodeData } from '../../types/workflow'
import BaseNode from './BaseNode'
import { nodeConfigs } from './nodeConfigs'

const MyCustomNode: React.FC<NodeProps<WorkflowNodeData>> = (props) => {
  return (
    <BaseNode
      {...props}
      config={nodeConfigs.myCustomType}
    />
  )
}

export default MyCustomNode
```

### 2. Thêm cấu hình cho node mới

Trong `nodeConfigs.ts`:

```tsx
export const nodeConfigs: Record<NodeType, BaseNodeConfig> = {
  // ... existing configs
  myCustomType: {
    backgroundColor: 'bg-purple-500',
    borderColor: 'border-purple-600',
    selectedBorderColor: 'border-purple-700',
    textColor: 'text-white',
    icon: '🔮',
    handles: {
      target: [
        {
          position: Position.Left,
          className: 'w-3 h-3 bg-purple-700'
        }
      ],
      source: [
        {
          position: Position.Right,
          className: 'w-3 h-3 bg-purple-700'
        }
      ]
    },
    editable: true
  }
}
```

### 3. Đăng ký node type

Trong `WorkflowBuilder.tsx`:

```tsx
const nodeTypes = {
  start: StartNode,
  process: ProcessNode,
  decision: DecisionNode,
  end: EndNode,
  myCustomType: MyCustomNode, // Thêm node mới
}
```

## Tính năng chính

### 1. Styling nhất quán
- Tất cả node đều có cùng cấu trúc CSS
- Màu sắc và border được định nghĩa trong config
- Responsive design với Tailwind CSS

### 2. Handle Management
- Hỗ trợ nhiều input/output handles
- Có thể tùy chỉnh vị trí, màu sắc và style
- Hỗ trợ handles có ID (cho decision node)

### 3. Inline Editing
- Double-click để edit (nếu được bật)
- Enter để save, Escape để cancel
- Tự động focus vào input khi edit

### 4. Context Integration
- Sử dụng WorkflowContext để cập nhật dữ liệu
- Tự động sync với workflow state

## Lợi ích

1. **Tái sử dụng code**: Giảm duplicate code giữa các node
2. **Nhất quán**: Tất cả node có cùng behavior và styling
3. **Dễ bảo trì**: Chỉ cần sửa BaseNode để ảnh hưởng tất cả node
4. **Mở rộng dễ dàng**: Thêm node mới chỉ cần tạo config
5. **Type Safety**: Sử dụng TypeScript để đảm bảo type safety

## Ví dụ Node hiện có

### StartNode
- Không có target handle
- Không thể edit
- Màu xanh lá

### ProcessNode
- Có cả target và source handle
- Có thể edit
- Màu xanh dương

### DecisionNode
- Có 1 target handle và 2 source handles (yes/no)
- Có thể edit
- Màu vàng

### EndNode
- Chỉ có target handle
- Không thể edit
- Màu đỏ
