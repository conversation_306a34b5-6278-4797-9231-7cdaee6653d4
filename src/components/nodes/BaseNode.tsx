import React, { useState, ReactNode } from 'react'
import { <PERSON>le, Position, NodeProps } from '@xyflow/react'
import { WorkflowNodeData, NodeType } from '../../types/workflow'
import { useWorkflowContext } from '../../contexts/WorkflowContext'

export interface BaseNodeConfig {
  // Styling
  backgroundColor: string
  borderColor: string
  selectedBorderColor: string
  textColor: string
  icon: string
  shape?: 'rectangle' | 'diamond' // Thêm thuộc tính shape

  // Handle configuration
  handles: {
    target?: {
      position: Position
      className?: string
      style?: React.CSSProperties
    }[]
    source?: {
      position: Position
      id?: string
      className?: string
      style?: React.CSSProperties
    }[]
  }

  // Behavior
  editable?: boolean
}

interface BaseNodeProps extends NodeProps<WorkflowNodeData> {
  config: BaseNodeConfig
  children?: ReactNode
}

const BaseNode: React.FC<BaseNodeProps> = ({
  data,
  selected,
  id,
  config,
  children
}) => {
  const { updateNodeData } = useWorkflowContext()
  const [isEditing, setIsEditing] = useState(false)
  const [label, setLabel] = useState(data.label)
  const [description, setDescription] = useState(data.description || '')

  const handleDoubleClick = () => {
    if (config.editable) {
      setIsEditing(true)
    }
  }

  const handleSave = () => {
    updateNodeData(id, { label, description })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setIsEditing(false)
    setLabel(data.label)
    setDescription(data.description || '')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave()
    }
    if (e.key === 'Escape') {
      handleCancel()
    }
  }

  // Tạo className dựa trên shape
  const getShapeClassName = () => {
    const baseClasses = `shadow-md border-2 ${config.backgroundColor} ${config.textColor} ${
      selected ? config.selectedBorderColor : config.borderColor
    }`

    if (config.shape === 'diamond') {
      return `${baseClasses} transform rotate-45 w-24 h-24 flex items-center justify-center relative outline outline-2 outline-red-500`
    }

    // Default rectangle shape
    return `${baseClasses} px-4 py-2 rounded-md`
  }

  const nodeClassName = getShapeClassName()

  // Render content dựa trên shape
  const renderContent = () => {
    if (config.shape === 'diamond') {
      return (
        <div className="transform -rotate-45 text-center">
          <div className="text-lg">{config.icon}</div>
          {!isEditing || !config.editable ? (
            <div className="text-xs font-bold mt-1">
              {data.label}
            </div>
          ) : (
            <input
              type="text"
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              onKeyDown={handleKeyPress}
              onBlur={handleSave}
              className={`text-xs font-bold bg-transparent border-b outline-none w-full text-center mt-1 ${
                config.textColor === 'text-white' ? 'border-white' : 'border-black'
              }`}
              autoFocus
            />
          )}
        </div>
      )
    }

    // Default rectangle content
    return (
      <div className="flex items-center">
        <div className="mr-2">{config.icon}</div>
        <div className="min-w-0 flex-1">
          {isEditing && config.editable ? (
            <div className="space-y-1">
              <input
                type="text"
                value={label}
                onChange={(e) => setLabel(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleSave}
                className={`text-lg font-bold bg-transparent border-b outline-none w-full ${
                  config.textColor === 'text-white' ? 'border-white' : 'border-black'
                }`}
                autoFocus
              />
              <input
                type="text"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleSave}
                className={`text-sm bg-transparent border-b outline-none w-full opacity-80 ${
                  config.textColor === 'text-white' ? 'border-white' : 'border-black'
                }`}
                placeholder="Description..."
              />
            </div>
          ) : (
            <div>
              <div className="text-lg font-bold">{data.label}</div>
              {data.description && (
                <div className="text-sm opacity-80">{data.description}</div>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={nodeClassName} onDoubleClick={handleDoubleClick}>
      {/* Target Handles */}
      {config.handles.target?.map((handle, index) => (
        <Handle
          key={`target-${index}`}
          type="target"
          position={handle.position}
          className={handle.className}
          style={handle.style}
        />
      ))}

      {renderContent()}

      {/* Custom content */}
      {children}

      {/* Source Handles */}
      {config.handles.source?.map((handle, index) => (
        <Handle
          key={`source-${index}`}
          type="source"
          position={handle.position}
          id={handle.id}
          className={handle.className}
          style={handle.style}
        />
      ))}
    </div>
  )
}

export default BaseNode
