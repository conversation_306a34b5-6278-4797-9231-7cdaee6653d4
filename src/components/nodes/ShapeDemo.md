# Demo Shape cho BaseNode

## Các hình dạng được hỗ trợ

### 1. Rectangle (<PERSON><PERSON><PERSON> chữ nhật)
- **Sử dụng**: Cho các node thông thường như Start, Process, End
- **Đặc điểm**:
  - Layout ngang với icon và text
  - Padding `px-4 py-2`
  - Border radius `rounded-md`
  - Hỗ trợ inline editing đầy đủ

### 2. Diamond (<PERSON><PERSON><PERSON> thôi)
- **Sử dụng**: Cho các node quyết định/điều kiện như Decision
- **Đặc điểm**:
  - Xoay 45 độ để tạo hình thôi
  - Kích thước cố định `w-24 h-24`
  - Content được xoay ngược lại -45 độ
  - Text nhỏ hơn để vừa trong hình thôi
  - Handles được định vị đặc biệt

## C<PERSON>ch handles được định vị

### Rectangle Nodes
```tsx
// Target handle - bên trái
{
  position: Position.Left,
  className: 'w-3 h-3 bg-blue-700'
}

// Source handle - bên phải
{
  position: Position.Right,
  className: 'w-3 h-3 bg-blue-700'
}
```

### Diamond Nodes
```tsx
// Target handle - đỉnh trái của hình thôi
{
  position: Position.Left,
  className: 'w-3 h-3 bg-yellow-700',
  style: {
    left: '0px',
    top: '50%',
    transform: 'translate(-50%, -50%)'
  }
}

// Source handles - các đỉnh còn lại (trên, phải, dưới)
{
  position: Position.Top,
  id: 'yes',
  className: 'w-3 h-3 bg-green-600',
  style: {
    left: '50%',
    top: '0px',
    transform: 'translate(-50%, -50%)'
  }
},
{
  position: Position.Right,
  id: 'no',
  className: 'w-3 h-3 bg-red-600',
  style: {
    left: '100%',
    top: '50%',
    transform: 'translate(-50%, -50%)'
  }
},
{
  position: Position.Bottom,
  id: 'maybe',
  className: 'w-3 h-3 bg-blue-600',
  style: {
    left: '50%',
    top: '100%',
    transform: 'translate(-50%, -50%)'
  }
}
```

## Ví dụ sử dụng

### Node hình chữ nhật
```tsx
const rectangleConfig: BaseNodeConfig = {
  backgroundColor: 'bg-blue-500',
  borderColor: 'border-blue-600',
  selectedBorderColor: 'border-blue-700',
  textColor: 'text-white',
  icon: '⚙️',
  shape: 'rectangle', // hoặc không cần khai báo (default)
  // ... handles config
}
```

### Node hình thôi
```tsx
const diamondConfig: BaseNodeConfig = {
  backgroundColor: 'bg-yellow-500',
  borderColor: 'border-yellow-600',
  selectedBorderColor: 'border-yellow-700',
  textColor: 'text-black',
  icon: '🔀',
  shape: 'diamond',
  // ... handles config với style đặc biệt
}
```

## Lưu ý khi thiết kế

1. **Diamond nodes**: Text nên ngắn gọn vì không gian hạn chế
2. **Handle positioning**: Diamond có handles ở 4 đỉnh (trái, trên, phải, dưới)
3. **Icon**: Chọn icon phù hợp với hình dạng và ý nghĩa của node
4. **Colors**: Màu sắc nên phản ánh chức năng (vàng cho decision, xanh cho process, v.v.)
5. **Edge connections**: Với diamond, edges sẽ kết nối tại các đỉnh, trông tự nhiên hơn

## Kết quả

- **StartNode**: Rectangle, xanh lá, icon ▶️, 1 source handle
- **ProcessNode**: Rectangle, xanh dương, icon ⚙️, 1 target + 1 source handle
- **DecisionNode**: Diamond, vàng, icon 🔀, 1 target + 3 source handles ở các đỉnh
- **EndNode**: Rectangle, đỏ, icon 🏁, 1 target handle

### Ưu điểm của handles ở đỉnh hình thôi:
- Kết nối edges tự nhiên hơn
- Phù hợp với chuẩn flowchart truyền thống
- Dễ nhận biết hướng đi của workflow
- Tạo sự khác biệt rõ ràng giữa decision và process nodes
